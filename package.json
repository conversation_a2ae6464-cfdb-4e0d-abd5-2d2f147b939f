{"name": "starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc ; vite build", "build-no-errors": "tsc ; vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^5.31.0", "@clerk/themes": "^2.2.41", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.2.8", "@tailwindcss/typography": "^0.5.16", "@tiptap/extension-character-count": "^2.26.1", "@tiptap/extension-color": "^2.26.1", "@tiptap/extension-document": "^2.11.7", "@tiptap/extension-focus": "^2.26.1", "@tiptap/extension-font-family": "^2.26.1", "@tiptap/extension-highlight": "^2.26.1", "@tiptap/extension-paragraph": "^2.11.7", "@tiptap/extension-placeholder": "^2.26.1", "@tiptap/extension-table": "^2.26.1", "@tiptap/extension-table-cell": "^2.26.1", "@tiptap/extension-table-header": "^2.26.1", "@tiptap/extension-table-row": "^2.26.1", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/extension-text-style": "^2.26.1", "@tiptap/extension-typography": "^2.26.1", "@tiptap/extension-underline": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@types/lodash": "^4.17.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "html2pdf.js": "^0.10.3", "lodash": "^4.17.21", "lucide-react": "^0.394.0", "pdfjs-dist": "^3.11.174", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-router": "^6.23.1", "react-router-dom": "^6.23.1", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.1", "zod": "^3.24.2", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.1", "@swc/core": "1.3.96", "@types/html2pdf.js": "^0.10.0", "@types/node": "^20.14.2", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react-swc": "3.5.0", "autoprefixer": "^10.4.19", "eslint": "^9.25.1", "eslint-plugin-react": "^7.37.5", "postcss": "^8.4.38", "tailwindcss": "3.4.1", "typescript": "^5.2.2", "typescript-eslint": "^8.31.0", "vite": "^5.2.0"}}