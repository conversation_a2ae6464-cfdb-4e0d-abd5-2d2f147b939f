import { Button } from '@/components/ui/button';
import { Brain, Users, CheckCircle, Sparkles, ArrowRight, AlertTriangle, Shield, Mail } from 'lucide-react';
import { useState } from 'react';

const ProductShowcase = () => {
  const [, setIsHovered] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  // Define the simplified 3-step workflow
  const steps = [
    {
      id: 'wizard',
      title: 'Smart Contract Wizard',
      description: 'AI-powered contract creation with live document preview',
      navItem: 'Contract Wizard',
      url: '/app/contracts/wizard'
    },
    {
      id: 'analysis',
      title: 'AI Contract Analysis',
      description: 'Real-time risk assessment and compliance checking',
      navItem: 'AI Analysis',
      url: '/app/contracts/analysis'
    },
    {
      id: 'finalize',
      title: 'Review & Finalize',
      description: 'Team collaboration and final approval workflow',
      navItem: 'Finalize',
      url: '/app/contracts/finalize'
    }
  ];

  // Manual step navigation
  const handleStepClick = (stepIndex: number) => {
    setCurrentStep(stepIndex);
  };

  const handleNextStep = () => {
    setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
  };

  const handlePrevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  return (
    <section className="py-8 relative">
      {/* Removed competing background decorations to use unified background */}

      <div className="relative px-6 lg:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-8">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full text-sm font-medium text-primary mb-6">
              <Sparkles className="w-4 h-4" />
              Live Demo
            </div>
            <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
              AI-Powered Contract Creation
            </h2>
            <p className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
              Watch how our intelligent contract wizard transforms legal document creation. From smart templates to real-time analysis, experience the future of contract management in three simple steps.
            </p>
          </div>

          {/* Main Demo Interface */}
          <div
            className="relative bg-background rounded-3xl shadow-2xl border overflow-hidden max-w-6xl mx-auto transition-all duration-500 hover:shadow-3xl group"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            {/* Apple-style window header */}
            <div className="flex items-center gap-2 px-6 py-4 border-b bg-gradient-to-r from-muted/50 to-muted/30">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full transition-all duration-300 group-hover:scale-110"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full transition-all duration-300 group-hover:scale-110" style={{transitionDelay: '50ms'}}></div>
                <div className="w-3 h-3 bg-green-500 rounded-full transition-all duration-300 group-hover:scale-110" style={{transitionDelay: '100ms'}}></div>
              </div>
            </div>

            <div className="p-4">
              {/* Step Indicator - Clickable */}
              <div className="flex items-center justify-center mb-4">
                <div className="flex items-center gap-4">
                  {steps.map((step, index) => (
                    <div key={step.id} className="flex items-center">
                      <button
                        onClick={() => handleStepClick(index)}
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 hover:scale-110 ${
                          index === currentStep
                            ? 'bg-primary text-primary-foreground shadow-lg'
                            : index < currentStep
                              ? 'bg-green-500 text-white hover:bg-green-600'
                              : 'bg-muted text-muted-foreground hover:bg-muted/80'
                        }`}
                        title={step.title}
                      >
                        {index < currentStep ? <CheckCircle className="w-4 h-4" /> : index + 1}
                      </button>
                      {index < steps.length - 1 && (
                        <div className={`w-12 h-0.5 mx-2 transition-all duration-300 ${
                          index < currentStep ? 'bg-green-500' : 'bg-muted'
                        }`} />
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Step Title and Description */}
              <div className="text-center mb-4">
                <h3 className="text-2xl md:text-3xl font-bold mb-2">
                  {steps[currentStep].title}
                </h3>
                <p className="text-muted-foreground text-lg mb-2">
                  {steps[currentStep].description}
                </p>
                <p className="text-sm text-muted-foreground/70">
                  Step {currentStep + 1} of {steps.length}
                </p>
              </div>

              {/* Step 1: Smart Contract Wizard */}
              {currentStep === 0 && (
                <div className="grid lg:grid-cols-5 gap-6">
                  {/* Document Preview - Left Panel (3/5 width) */}
                  <div className="lg:col-span-3 space-y-4">
                    {/* Document Header */}
                    <div className="bg-white border border-border rounded-lg shadow-sm">
                      {/* Simple Toolbar */}
                      <div className="flex items-center justify-between px-4 py-3 border-b bg-gray-50">
                        <span className="text-sm font-medium">Contract Preview</span>
                        <div className="flex items-center gap-2">
                          <Button size="sm" variant="outline">
                            Export
                          </Button>
                        </div>
                      </div>

                      {/* Document Content */}
                      <div className="p-4 min-h-[280px] bg-white">
                        <div className="max-w-none">
                          <div className="text-center mb-4">
                            <h1 className="text-2xl font-bold mb-4">CONTRACT TITLE</h1>
                          </div>
                          
                          <div className="space-y-4 text-sm leading-relaxed">
                            <div>
                              <p className="font-semibold mb-3">Service Agreement</p>
                              <p className="text-muted-foreground mb-2">Contract No.: CONT-5042</p>
                              <p className="text-muted-foreground mb-4">Effective Date: August 15, 2025</p>
                            </div>
                            
                            <div className="border-l-4 border-primary pl-4">
                              <p className="font-semibold mb-2">Parties</p>
                              <p className="mb-2">• Client: [Company Name]</p>
                              <p className="mb-4">• Service Provider: [Provider Name]</p>
                            </div>
                            
                            <div className="border-l-4 border-blue-400 pl-4">
                              <p className="font-semibold mb-2">Key Terms</p>
                              <p className="mb-2">• Duration: 12 months</p>
                              <p className="mb-2">• Payment: Monthly installments</p>
                              <p className="mb-2">• Deliverables: As specified in Exhibit A</p>
                            </div>
                            
                            <div className="bg-gray-50 p-3 rounded">
                              <p className="text-xs text-muted-foreground">✨ AI is generating contract clauses based on your selections...</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Form Panel - Right Panel (2/5 width) */}
                  <div className="lg:col-span-2 space-y-4">
                    {/* Contract Setup */}
                    <div className="bg-white border border-border rounded-lg p-4 shadow-sm">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-lg font-semibold">Contract Setup</h4>
                      </div>
                      
                      <div className="space-y-4">
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium mb-2">
                              Contract Type <span className="text-red-500">*</span>
                            </label>
                            <select className="w-full p-3 border border-border rounded-lg bg-background">
                              <option>Service Agreement</option>
                              <option>Software Development</option>
                              <option>Consulting Agreement</option>
                              <option>Employment Contract</option>
                              <option>NDA</option>
                            </select>
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium mb-2">
                              Jurisdiction <span className="text-red-500">*</span>
                            </label>
                            <select className="w-full p-3 border border-border rounded-lg bg-background">
                              <option>United States</option>
                              <option>United Kingdom</option>
                              <option>European Union</option>
                              <option>Canada</option>
                            </select>
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium mb-2">
                              Duration
                            </label>
                            <select className="w-full p-3 border border-border rounded-lg bg-background">
                              <option>12 months</option>
                              <option>6 months</option>
                              <option>24 months</option>
                              <option>Custom</option>
                            </select>
                          </div>
                        </div>
                        
                        <div className="flex justify-between items-center pt-4">
                          <Button variant="outline">
                            Back
                          </Button>
                          <Button onClick={handleNextStep}>
                            Continue
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* AI Status Panel */}
                    <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Brain className="w-5 h-5 text-green-600" />
                        <span className="font-semibold text-green-900">AI Assistant</span>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm text-green-800">✓ Template loaded</p>
                        <p className="text-sm text-green-800">✓ Compliance checked</p>
                        <p className="text-sm text-green-800">⚡ Generating clauses...</p>
                      </div>
                    </div>
                  </div>
                </div>

              )}

              {/* Step 1 Navigation Controls */}
              {currentStep === 0 && (
                <div className="flex justify-center gap-4 mt-6">
                  <Button 
                    onClick={handlePrevStep} 
                    disabled={currentStep === 0}
                    variant="outline"
                    className="px-6"
                  >
                    Previous Step
                  </Button>
                  <Button 
                    onClick={handleNextStep} 
                    disabled={currentStep >= steps.length - 1}
                    className="px-6"
                  >
                    Next Step
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              )}

              {/* Step 2: AI Contract Analysis */}
              {currentStep === 1 && (
                <div className="space-y-6">
                  {/* Analysis Header */}
                  <div className="text-center">
                    <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full mb-4">
                      <Brain className="w-5 h-5 text-blue-600" />
                      <span className="text-sm font-medium text-blue-800">AI Analysis Complete</span>
                    </div>
                    <p className="text-muted-foreground">Contract reviewed in 2.3 seconds</p>
                  </div>

                  {/* Simplified Analysis Results */}
                  <div className="grid lg:grid-cols-2 gap-6">
                    {/* Overall Score */}
                    <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <Shield className="w-8 h-8 text-green-600" />
                          <div>
                            <h4 className="font-semibold text-green-900">Contract Score</h4>
                            <p className="text-sm text-green-700">Overall assessment</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-4xl font-bold text-green-600">96%</div>
                          <div className="text-sm text-green-700">Excellent</div>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="flex justify-between text-sm">
                          <span className="text-green-700">Risk Level</span>
                          <span className="text-green-800 font-medium">Low</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-green-700">Compliance</span>
                          <span className="text-green-800 font-medium">✓ Passed</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-green-700">Legal Review</span>
                          <span className="text-green-800 font-medium">✓ Ready</span>
                        </div>
                      </div>
                    </div>

                    {/* Key Issues */}
                    <div className="bg-white border border-border rounded-xl p-6">
                      <h4 className="font-semibold mb-4">Key Findings</h4>
                      <div className="space-y-3">
                        <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                          <CheckCircle className="w-5 h-5 text-green-600" />
                          <div>
                            <p className="font-medium text-green-900">Payment Terms</p>
                            <p className="text-xs text-green-700">Well-structured milestone payments</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
                          <AlertTriangle className="w-5 h-5 text-yellow-600" />
                          <div>
                            <p className="font-medium text-yellow-900">IP Rights</p>
                            <p className="text-xs text-yellow-700">Consider adding ownership clause</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                          <Brain className="w-5 h-5 text-blue-600" />
                          <div>
                            <p className="font-medium text-blue-900">AI Suggestion</p>
                            <p className="text-xs text-blue-700">Add force majeure protection</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>


                  {/* Action Buttons */}
                  <div className="flex justify-center gap-4">
                    <Button onClick={handlePrevStep} variant="outline">
                      Back
                    </Button>
                    <Button onClick={handleNextStep} className="px-8">
                      Continue to Review
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                </div>
              )}

              {/* Step 3: Review & Finalize */}
              {currentStep === 2 && (
                <div className="space-y-6">
                  {/* Header */}
                  <div className="text-center">
                    <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-green-100 to-emerald-100 rounded-full mb-4">
                      <Users className="w-5 h-5 text-green-600" />
                      <span className="text-sm font-medium text-green-800">Ready for Signatures</span>
                    </div>
                    <p className="text-muted-foreground">Contract approved and ready to finalize</p>
                  </div>

                  {/* Simplified Final Review */}
                  <div className="grid lg:grid-cols-2 gap-6">
                    {/* Contract Summary */}
                    <div className="bg-white border border-border rounded-xl p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-lg font-semibold">Contract Summary</h4>
                        <span className="text-xs bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">
                          ✓ Ready
                        </span>
                      </div>
                      
                      <div className="space-y-4">
                        <div className="text-center pb-4 border-b">
                          <h5 className="text-lg font-bold text-gray-900">Service Agreement</h5>
                          <p className="text-sm text-gray-600 mt-1">12-month contract • $85,000 value</p>
                        </div>
                        
                        <div className="space-y-3">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Contract Score</span>
                            <span className="font-medium text-green-600">96% Excellent</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Risk Level</span>
                            <span className="font-medium text-green-600">Low</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Compliance</span>
                            <span className="font-medium text-green-600">✓ Passed</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Team Approval</span>
                            <span className="font-medium text-green-600">✓ Complete</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Team Status */}
                    <div className="bg-white border border-border rounded-xl p-6">
                      <h4 className="text-lg font-semibold mb-4">Team Approvals</h4>
                      
                      <div className="space-y-3">
                        <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                          <CheckCircle className="w-5 h-5 text-green-600" />
                          <div>
                            <p className="font-medium text-green-900">Legal Review</p>
                            <p className="text-xs text-green-700">Sarah Chen • Approved</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                          <CheckCircle className="w-5 h-5 text-green-600" />
                          <div>
                            <p className="font-medium text-green-900">Finance Approval</p>
                            <p className="text-xs text-green-700">Mike Rodriguez • Approved</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                          <Mail className="w-5 h-5 text-blue-600" />
                          <div>
                            <p className="font-medium text-blue-900">Ready for Signatures</p>
                            <p className="text-xs text-blue-700">Awaiting client signature</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Next Steps */}
                  <div className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-xl p-6 border border-primary/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-lg font-semibold text-primary">🎉 Contract Complete!</h4>
                        <p className="text-sm text-muted-foreground mt-1">Ready for digital signatures and execution</p>
                      </div>
                      <Button className="px-6">
                        Send for Signatures
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  </div>

                  {/* Final Actions */}
                  <div className="flex justify-center gap-4">
                    <Button onClick={handlePrevStep} variant="outline">
                      Back
                    </Button>
                    <Button onClick={() => setCurrentStep(0)} variant="outline">
                      Start New Contract
                    </Button>
                  </div>
                </div>
              )}

            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProductShowcase;
